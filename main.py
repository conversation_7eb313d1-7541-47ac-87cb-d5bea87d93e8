#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV文件合并工具 - 主启动文件
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

try:
    from csv_merger_gui import main as gui_main
    from csv_merger import main as cli_main
except ImportError as e:
    print(f"导入模块失败: {e}")
    sys.exit(1)


def show_help():
    """显示帮助信息"""
    help_text = """
CSV文件合并工具

用法:
    python main.py [选项]

选项:
    --gui, -g     启动图形界面（默认）
    --cli, -c     使用命令行模式
    --help, -h    显示此帮助信息

功能特性:
1. 支持1-6层文件夹扫描（默认2层）
2. 两种合并模式：
   - 按文件名合并：将同名CSV文件合并
   - 直接合并：将所有表头一致的CSV文件合并
3. 表头一致性检查和孤立文件处理
4. 多列排序功能（可设置优先级）
5. 去重功能（需启用排序）
6. 友好的图形界面

示例:
    python main.py              # 启动GUI界面
    python main.py --gui        # 启动GUI界面
    python main.py --cli        # 使用命令行模式测试
"""
    print(help_text)


def main():
    """主函数"""
    args = sys.argv[1:]
    
    # 解析命令行参数
    if '--help' in args or '-h' in args:
        show_help()
        return
    
    if '--cli' in args or '-c' in args:
        print("启动命令行模式...")
        cli_main()
    else:
        print("启动图形界面...")
        gui_main()


if __name__ == "__main__":
    main()
