#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV文件合并程序 - 卡通风格GUI界面
使用tkinter创建卡通风格的用户友好图形界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from pathlib import Path
from csv_merger import CSVMerger
import logging
from PIL import Image, ImageTk, ImageFilter
import math


class CartoonStyle:
    """卡通风格样式配置"""
    
    # 颜色配置
    COLORS = {
        'primary': '#FF6B9D',      # 粉红色主色调
        'secondary': '#4ECDC4',    # 青绿色辅助色
        'accent': '#FFE66D',       # 黄色强调色
        'success': '#95E1D3',      # 成功绿色
        'warning': '#F38BA8',      # 警告橙色
        'danger': '#FF8A80',       # 危险红色
        'background': '#F8F9FA',   # 背景色
        'surface': '#FFFFFF',      # 表面色
        'text': '#2D3436',         # 文字色
        'text_light': '#636E72',   # 浅色文字
        'border': '#DDD6FE',       # 边框色
    }
    
    # 字体配置
    FONTS = {
        'title': ('Comic Sans MS', 18, 'bold'),
        'heading': ('Comic Sans MS', 14, 'bold'),
        'body': ('Comic Sans MS', 11),
        'small': ('Comic Sans MS', 9),
        'button': ('Comic Sans MS', 11, 'bold'),
    }
    
    @staticmethod
    def create_rounded_button_style():
        """创建圆角按钮样式"""
        style = ttk.Style()
        
        # 配置按钮样式
        style.configure(
            'Cartoon.TButton',
            font=CartoonStyle.FONTS['button'],
            borderwidth=3,
            relief='flat',
            padding=(20, 10),
            background=CartoonStyle.COLORS['primary'],
            foreground='white',
            focuscolor='none'
        )
        
        style.map(
            'Cartoon.TButton',
            background=[
                ('active', CartoonStyle.COLORS['secondary']),
                ('pressed', CartoonStyle.COLORS['accent'])
            ],
            relief=[('pressed', 'flat'), ('!pressed', 'flat')]
        )
        
        # 配置成功按钮样式
        style.configure(
            'Success.TButton',
            font=CartoonStyle.FONTS['button'],
            borderwidth=3,
            relief='flat',
            padding=(20, 10),
            background=CartoonStyle.COLORS['success'],
            foreground='white',
            focuscolor='none'
        )
        
        # 配置警告按钮样式
        style.configure(
            'Warning.TButton',
            font=CartoonStyle.FONTS['button'],
            borderwidth=3,
            relief='flat',
            padding=(15, 8),
            background=CartoonStyle.COLORS['warning'],
            foreground='white',
            focuscolor='none'
        )
        
        return style


class BackgroundManager:
    """背景图片管理器"""
    
    def __init__(self, background_folder="background"):
        self.background_folder = Path(background_folder)
        self.current_image = None
        self.current_photo = None
        
    def load_background_image(self, image_path, window_size, alpha=0.3):
        """加载并处理背景图片"""
        try:
            # 打开图片
            image = Image.open(image_path)
            
            # 调整图片大小以适应窗口
            image = image.resize(window_size, Image.Resampling.LANCZOS)
            
            # 应用模糊效果
            image = image.filter(ImageFilter.GaussianBlur(radius=2))
            
            # 调整透明度
            if image.mode != 'RGBA':
                image = image.convert('RGBA')
            
            # 创建透明度遮罩
            overlay = Image.new('RGBA', image.size, (255, 255, 255, int(255 * (1 - alpha))))
            image = Image.alpha_composite(image, overlay)
            
            # 转换为PhotoImage
            self.current_photo = ImageTk.PhotoImage(image)
            self.current_image = image
            
            return self.current_photo
            
        except Exception as e:
            print(f"加载背景图片失败: {e}")
            return None
    
    def get_available_images(self):
        """获取可用的背景图片列表"""
        if not self.background_folder.exists():
            return []
        
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.gif'}
        images = []
        
        for file_path in self.background_folder.iterdir():
            if file_path.suffix.lower() in image_extensions:
                images.append(file_path)
        
        return images


class CSVMergerCartoonGUI:
    """CSV合并程序卡通风格GUI界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("🎨 CSV文件合并工具 - 卡通版")
        self.root.geometry("900x800")
        self.root.resizable(True, True)
        
        # 设置窗口图标和样式
        self.setup_window_style()
        
        # 初始化背景管理器
        self.bg_manager = BackgroundManager()
        
        # 初始化合并器
        self.merger = CSVMerger()
        
        # 界面变量
        self.source_folder_var = tk.StringVar()
        self.output_folder_var = tk.StringVar()
        self.max_depth_var = tk.IntVar(value=2)
        self.merge_mode_var = tk.StringVar(value="filename")
        self.enable_sorting_var = tk.BooleanVar(value=False)
        self.enable_dedup_var = tk.BooleanVar(value=False)
        self.sort_columns_var = tk.StringVar(value="日期")
        self.encoding_var = tk.StringVar(value="UTF-8(BOM)")
        
        # 创建样式
        self.style = CartoonStyle.create_rounded_button_style()
        
        self.setup_ui()
        self.setup_logging()
        self.load_background()
        
    def setup_window_style(self):
        """设置窗口样式"""
        self.root.configure(bg=CartoonStyle.COLORS['background'])
        
        # 尝试设置窗口图标
        try:
            # 这里可以设置一个图标文件
            pass
        except:
            pass
    
    def load_background(self):
        """加载背景图片"""
        available_images = self.bg_manager.get_available_images()
        if available_images:
            # 使用第一张可用图片作为背景
            bg_image = self.bg_manager.load_background_image(
                available_images[0], 
                (900, 800), 
                alpha=0.15
            )
            
            if bg_image:
                # 创建背景标签
                self.bg_label = tk.Label(self.root, image=bg_image)
                self.bg_label.place(x=0, y=0, relwidth=1, relheight=1)
                self.bg_label.image = bg_image  # 保持引用
    
    def create_rounded_frame(self, parent, bg_color, border_color=None, padding=20):
        """创建圆角框架"""
        frame = tk.Frame(
            parent,
            bg=bg_color,
            relief='flat',
            bd=0,
            padx=padding,
            pady=padding
        )
        
        if border_color:
            # 添加边框效果
            border_frame = tk.Frame(
                parent,
                bg=border_color,
                relief='flat',
                bd=0,
                padx=2,
                pady=2
            )
            frame.pack(in_=border_frame, fill='both', expand=True)
            return border_frame, frame
        
        return frame
    
    def create_cute_label(self, parent, text, font_key='body', color=None):
        """创建可爱的标签"""
        if color is None:
            color = CartoonStyle.COLORS['text']
            
        label = tk.Label(
            parent,
            text=text,
            font=CartoonStyle.FONTS[font_key],
            fg=color,
            bg=CartoonStyle.COLORS['surface'],
            relief='flat'
        )
        return label
    
    def create_cute_entry(self, parent, textvariable, width=40):
        """创建可爱的输入框"""
        entry = tk.Entry(
            parent,
            textvariable=textvariable,
            font=CartoonStyle.FONTS['body'],
            width=width,
            relief='flat',
            bd=3,
            bg=CartoonStyle.COLORS['surface'],
            fg=CartoonStyle.COLORS['text'],
            insertbackground=CartoonStyle.COLORS['primary']
        )
        return entry

    def setup_ui(self):
        """设置用户界面"""
        # 创建主滚动框架
        main_canvas = tk.Canvas(
            self.root,
            bg=CartoonStyle.COLORS['background'],
            highlightthickness=0
        )
        scrollbar = ttk.Scrollbar(self.root, orient="vertical", command=main_canvas.yview)
        scrollable_frame = tk.Frame(main_canvas, bg=CartoonStyle.COLORS['background'])

        scrollable_frame.bind(
            "<Configure>",
            lambda e: main_canvas.configure(scrollregion=main_canvas.bbox("all"))
        )

        main_canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        main_canvas.configure(yscrollcommand=scrollbar.set)

        main_canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # 主容器
        main_frame = tk.Frame(scrollable_frame, bg=CartoonStyle.COLORS['background'], padx=30, pady=20)
        main_frame.pack(fill='both', expand=True)

        # 标题区域
        self.create_title_section(main_frame)

        # 文件选择区域
        self.create_file_selection_section(main_frame)

        # 配置选项区域
        self.create_config_section(main_frame)

        # 操作按钮区域
        self.create_action_section(main_frame)

        # 日志显示区域
        self.create_log_section(main_frame)

    def create_title_section(self, parent):
        """创建标题区域"""
        title_frame = self.create_rounded_frame(
            parent,
            CartoonStyle.COLORS['surface'],
            CartoonStyle.COLORS['border']
        )
        title_frame.pack(fill='x', pady=(0, 20))

        # 主标题
        title_label = tk.Label(
            title_frame,
            text="🎨 CSV文件合并工具",
            font=CartoonStyle.FONTS['title'],
            fg=CartoonStyle.COLORS['primary'],
            bg=CartoonStyle.COLORS['surface']
        )
        title_label.pack(pady=15)

        # 副标题
        subtitle_label = tk.Label(
            title_frame,
            text="✨ 让数据合并变得简单有趣 ✨",
            font=CartoonStyle.FONTS['body'],
            fg=CartoonStyle.COLORS['text_light'],
            bg=CartoonStyle.COLORS['surface']
        )
        subtitle_label.pack(pady=(0, 10))

    def create_file_selection_section(self, parent):
        """创建文件选择区域"""
        file_frame = self.create_rounded_frame(
            parent,
            CartoonStyle.COLORS['surface'],
            CartoonStyle.COLORS['border']
        )
        file_frame.pack(fill='x', pady=(0, 15))

        # 区域标题
        section_title = self.create_cute_label(
            file_frame,
            "📁 文件夹选择",
            'heading',
            CartoonStyle.COLORS['secondary']
        )
        section_title.pack(anchor='w', pady=(0, 15))

        # 源文件夹选择
        source_row = tk.Frame(file_frame, bg=CartoonStyle.COLORS['surface'])
        source_row.pack(fill='x', pady=5)

        source_label = self.create_cute_label(source_row, "📂 源文件夹:")
        source_label.pack(side='left', padx=(0, 10))

        source_entry = self.create_cute_entry(source_row, self.source_folder_var, 35)
        source_entry.pack(side='left', padx=(0, 10), fill='x', expand=True)

        source_btn = ttk.Button(
            source_row,
            text="🔍 浏览",
            command=self.browse_source_folder,
            style='Cartoon.TButton'
        )
        source_btn.pack(side='right')

        # 输出文件夹选择
        output_row = tk.Frame(file_frame, bg=CartoonStyle.COLORS['surface'])
        output_row.pack(fill='x', pady=5)

        output_label = self.create_cute_label(output_row, "💾 输出文件夹:")
        output_label.pack(side='left', padx=(0, 10))

        output_entry = self.create_cute_entry(output_row, self.output_folder_var, 35)
        output_entry.pack(side='left', padx=(0, 10), fill='x', expand=True)

        output_btn = ttk.Button(
            output_row,
            text="🔍 浏览",
            command=self.browse_output_folder,
            style='Cartoon.TButton'
        )
        output_btn.pack(side='right')

    def create_config_section(self, parent):
        """创建配置选项区域"""
        config_frame = self.create_rounded_frame(
            parent,
            CartoonStyle.COLORS['surface'],
            CartoonStyle.COLORS['border']
        )
        config_frame.pack(fill='x', pady=(0, 15))

        # 区域标题
        section_title = self.create_cute_label(
            config_frame,
            "⚙️ 配置选项",
            'heading',
            CartoonStyle.COLORS['accent']
        )
        section_title.pack(anchor='w', pady=(0, 15))

        # 创建两列布局
        config_container = tk.Frame(config_frame, bg=CartoonStyle.COLORS['surface'])
        config_container.pack(fill='x')

        left_column = tk.Frame(config_container, bg=CartoonStyle.COLORS['surface'])
        left_column.pack(side='left', fill='both', expand=True, padx=(0, 15))

        right_column = tk.Frame(config_container, bg=CartoonStyle.COLORS['surface'])
        right_column.pack(side='right', fill='both', expand=True)

        # 左列配置项
        self.create_depth_config(left_column)
        self.create_merge_mode_config(left_column)
        self.create_encoding_config(left_column)

        # 右列配置项
        self.create_sorting_config(right_column)
        self.create_dedup_config(right_column)

    def create_depth_config(self, parent):
        """创建扫描深度配置"""
        depth_frame = tk.Frame(parent, bg=CartoonStyle.COLORS['surface'])
        depth_frame.pack(fill='x', pady=10)

        depth_label = self.create_cute_label(depth_frame, "🔍 扫描深度:")
        depth_label.pack(anchor='w')

        depth_control = tk.Frame(depth_frame, bg=CartoonStyle.COLORS['surface'])
        depth_control.pack(fill='x', pady=5)

        depth_scale = tk.Scale(
            depth_control,
            from_=1, to=6,
            orient=tk.HORIZONTAL,
            variable=self.max_depth_var,
            command=self.on_depth_change,
            font=CartoonStyle.FONTS['body'],
            bg=CartoonStyle.COLORS['surface'],
            fg=CartoonStyle.COLORS['text'],
            activebackground=CartoonStyle.COLORS['primary'],
            highlightthickness=0,
            troughcolor=CartoonStyle.COLORS['border'],
            length=200
        )
        depth_scale.pack(side='left', fill='x', expand=True)

        self.depth_label = self.create_cute_label(depth_control, "2层")
        self.depth_label.pack(side='right', padx=(10, 0))

    def create_merge_mode_config(self, parent):
        """创建合并模式配置"""
        mode_frame = tk.Frame(parent, bg=CartoonStyle.COLORS['surface'])
        mode_frame.pack(fill='x', pady=10)

        mode_label = self.create_cute_label(mode_frame, "🔄 合并模式:")
        mode_label.pack(anchor='w')

        mode_control = tk.Frame(mode_frame, bg=CartoonStyle.COLORS['surface'])
        mode_control.pack(fill='x', pady=5)

        filename_radio = tk.Radiobutton(
            mode_control,
            text="📝 按文件名合并",
            variable=self.merge_mode_var,
            value="filename",
            font=CartoonStyle.FONTS['body'],
            bg=CartoonStyle.COLORS['surface'],
            fg=CartoonStyle.COLORS['text'],
            selectcolor=CartoonStyle.COLORS['primary'],
            activebackground=CartoonStyle.COLORS['surface']
        )
        filename_radio.pack(anchor='w')

        direct_radio = tk.Radiobutton(
            mode_control,
            text="🔗 直接合并",
            variable=self.merge_mode_var,
            value="direct",
            font=CartoonStyle.FONTS['body'],
            bg=CartoonStyle.COLORS['surface'],
            fg=CartoonStyle.COLORS['text'],
            selectcolor=CartoonStyle.COLORS['primary'],
            activebackground=CartoonStyle.COLORS['surface']
        )
        direct_radio.pack(anchor='w')

    def create_encoding_config(self, parent):
        """创建编码格式配置"""
        encoding_frame = tk.Frame(parent, bg=CartoonStyle.COLORS['surface'])
        encoding_frame.pack(fill='x', pady=10)

        encoding_label = self.create_cute_label(encoding_frame, "📄 输出编码:")
        encoding_label.pack(anchor='w')

        encoding_combo = ttk.Combobox(
            encoding_frame,
            textvariable=self.encoding_var,
            values=list(self.merger.encoding_options.keys()),
            state="readonly",
            font=CartoonStyle.FONTS['body'],
            width=20
        )
        encoding_combo.pack(anchor='w', pady=5)
        encoding_combo.set("UTF-8(BOM)")

    def create_sorting_config(self, parent):
        """创建排序配置"""
        sort_frame = tk.Frame(parent, bg=CartoonStyle.COLORS['surface'])
        sort_frame.pack(fill='x', pady=10)

        sort_check = tk.Checkbutton(
            sort_frame,
            text="📊 启用排序功能",
            variable=self.enable_sorting_var,
            command=self.on_sorting_toggle,
            font=CartoonStyle.FONTS['body'],
            bg=CartoonStyle.COLORS['surface'],
            fg=CartoonStyle.COLORS['text'],
            selectcolor=CartoonStyle.COLORS['success'],
            activebackground=CartoonStyle.COLORS['surface']
        )
        sort_check.pack(anchor='w')

        sort_input_frame = tk.Frame(sort_frame, bg=CartoonStyle.COLORS['surface'])
        sort_input_frame.pack(fill='x', pady=5)

        sort_label = self.create_cute_label(sort_input_frame, "排序列:")
        sort_label.pack(side='left')

        self.sort_entry = self.create_cute_entry(sort_input_frame, self.sort_columns_var, 20)
        self.sort_entry.pack(side='left', padx=(5, 0), fill='x', expand=True)
        self.sort_entry.config(state='disabled')

    def create_dedup_config(self, parent):
        """创建去重配置"""
        dedup_frame = tk.Frame(parent, bg=CartoonStyle.COLORS['surface'])
        dedup_frame.pack(fill='x', pady=10)

        self.dedup_check = tk.Checkbutton(
            dedup_frame,
            text="🧹 启用去重功能",
            variable=self.enable_dedup_var,
            font=CartoonStyle.FONTS['body'],
            bg=CartoonStyle.COLORS['surface'],
            fg=CartoonStyle.COLORS['text'],
            selectcolor=CartoonStyle.COLORS['success'],
            activebackground=CartoonStyle.COLORS['surface'],
            state='disabled'
        )
        self.dedup_check.pack(anchor='w')

        dedup_note = self.create_cute_label(
            dedup_frame,
            "💡 需先启用排序功能",
            'small',
            CartoonStyle.COLORS['text_light']
        )
        dedup_note.pack(anchor='w', padx=(20, 0))

    def create_action_section(self, parent):
        """创建操作按钮区域"""
        action_frame = self.create_rounded_frame(
            parent,
            CartoonStyle.COLORS['surface'],
            CartoonStyle.COLORS['border']
        )
        action_frame.pack(fill='x', pady=(0, 15))

        # 按钮容器
        button_container = tk.Frame(action_frame, bg=CartoonStyle.COLORS['surface'])
        button_container.pack(pady=20)

        # 主要操作按钮
        self.merge_button = ttk.Button(
            button_container,
            text="🚀 开始合并",
            command=self.start_merge,
            style='Success.TButton'
        )
        self.merge_button.pack(side='left', padx=10)

        # 辅助按钮
        clear_btn = ttk.Button(
            button_container,
            text="🧹 清空日志",
            command=self.clear_log,
            style='Warning.TButton'
        )
        clear_btn.pack(side='left', padx=10)

        open_btn = ttk.Button(
            button_container,
            text="📁 打开输出文件夹",
            command=self.open_output_folder,
            style='Cartoon.TButton'
        )
        open_btn.pack(side='left', padx=10)

        # 进度条
        self.progress = ttk.Progressbar(
            action_frame,
            mode='indeterminate',
            style='Cartoon.Horizontal.TProgressbar'
        )
        self.progress.pack(fill='x', pady=(10, 0))

    def create_log_section(self, parent):
        """创建日志显示区域"""
        log_frame = self.create_rounded_frame(
            parent,
            CartoonStyle.COLORS['surface'],
            CartoonStyle.COLORS['border']
        )
        log_frame.pack(fill='both', expand=True)

        # 区域标题
        log_title = self.create_cute_label(
            log_frame,
            "📋 处理日志",
            'heading',
            CartoonStyle.COLORS['primary']
        )
        log_title.pack(anchor='w', pady=(0, 10))

        # 日志文本区域
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=12,
            width=80,
            font=CartoonStyle.FONTS['small'],
            bg=CartoonStyle.COLORS['background'],
            fg=CartoonStyle.COLORS['text'],
            relief='flat',
            bd=2,
            wrap=tk.WORD
        )
        self.log_text.pack(fill='both', expand=True)

    def setup_logging(self):
        """设置日志处理"""
        class GUILogHandler(logging.Handler):
            def __init__(self, text_widget):
                super().__init__()
                self.text_widget = text_widget

            def emit(self, record):
                msg = self.format(record)
                # 添加表情符号
                if record.levelname == 'INFO':
                    msg = f"ℹ️ {msg}"
                elif record.levelname == 'WARNING':
                    msg = f"⚠️ {msg}"
                elif record.levelname == 'ERROR':
                    msg = f"❌ {msg}"

                self.text_widget.insert(tk.END, msg + '\n')
                self.text_widget.see(tk.END)
                self.text_widget.update()

        # 配置日志
        logger = logging.getLogger('csv_merger')
        logger.setLevel(logging.INFO)

        # 清除现有处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # 添加GUI处理器
        gui_handler = GUILogHandler(self.log_text)
        gui_handler.setFormatter(logging.Formatter('%(asctime)s - %(message)s'))
        logger.addHandler(gui_handler)

    # 事件处理方法
    def on_depth_change(self, value):
        """深度滑块变化事件"""
        depth = int(float(value))
        self.depth_label.config(text=f"{depth}层")

    def on_sorting_toggle(self):
        """排序选项切换事件"""
        if self.enable_sorting_var.get():
            self.sort_entry.config(state='normal')
            self.dedup_check.config(state='normal')
        else:
            self.sort_entry.config(state='disabled')
            self.dedup_check.config(state='disabled')
            self.enable_dedup_var.set(False)

    def browse_source_folder(self):
        """浏览源文件夹"""
        folder = filedialog.askdirectory(title="选择源文件夹")
        if folder:
            self.source_folder_var.set(folder)

    def browse_output_folder(self):
        """浏览输出文件夹"""
        folder = filedialog.askdirectory(title="选择输出文件夹")
        if folder:
            self.output_folder_var.set(folder)

    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)

    def open_output_folder(self):
        """打开输出文件夹"""
        output_folder = self.output_folder_var.get()
        if output_folder and os.path.exists(output_folder):
            os.startfile(output_folder)
        else:
            messagebox.showwarning("警告", "输出文件夹不存在或未设置")

    def validate_inputs(self):
        """验证输入参数"""
        if not self.source_folder_var.get():
            messagebox.showerror("错误", "请选择源文件夹")
            return False

        if not self.output_folder_var.get():
            messagebox.showerror("错误", "请选择输出文件夹")
            return False

        if not os.path.exists(self.source_folder_var.get()):
            messagebox.showerror("错误", "源文件夹不存在")
            return False

        return True

    def start_merge(self):
        """开始合并处理"""
        if not self.validate_inputs():
            return

        # 禁用合并按钮，显示进度条
        self.merge_button.config(state='disabled')
        self.progress.start()

        # 在后台线程中执行合并
        thread = threading.Thread(target=self.perform_merge)
        thread.daemon = True
        thread.start()

    def perform_merge(self):
        """执行合并操作（在后台线程中运行）"""
        try:
            # 配置合并器参数
            sort_columns = []
            if self.enable_sorting_var.get() and self.sort_columns_var.get().strip():
                sort_columns = [col.strip() for col in self.sort_columns_var.get().split(',')]

            # 获取选择的编码格式
            encoding_name = self.encoding_var.get()
            encoding_value = self.merger.encoding_options.get(encoding_name, "utf-8-sig")

            self.merger.set_config(
                max_depth=self.max_depth_var.get(),
                merge_by_filename=(self.merge_mode_var.get() == "filename"),
                enable_sorting=self.enable_sorting_var.get(),
                sort_columns=sort_columns,
                enable_deduplication=self.enable_dedup_var.get(),
                output_encoding=encoding_value
            )

            # 执行合并
            result = self.merger.merge_files(
                self.source_folder_var.get(),
                self.output_folder_var.get()
            )

            # 在主线程中显示结果
            self.root.after(0, self.merge_completed, result)

        except Exception as e:
            error_msg = f"合并过程中发生错误: {str(e)}"
            self.root.after(0, self.merge_error, error_msg)

    def merge_completed(self, result):
        """合并完成回调"""
        self.progress.stop()
        self.merge_button.config(state='normal')

        if result.get('success', False):
            message = f"""🎉 合并完成！

📊 处理统计：
• 总文件数: {result['total_files']}
• 合并组数: {result['merged_groups']}
• 孤立文件数: {result['orphan_files']}
• 输出文件夹: {result['output_folder']}

✨ 所有文件已成功处理！"""

            messagebox.showinfo("合并完成", message)
        else:
            error_msg = result.get('error', '未知错误')
            messagebox.showerror("合并失败", f"❌ 合并失败: {error_msg}")

    def merge_error(self, error_msg):
        """合并错误回调"""
        self.progress.stop()
        self.merge_button.config(state='normal')
        messagebox.showerror("错误", f"❌ {error_msg}")


def main():
    """主函数"""
    # 检查PIL依赖
    try:
        from PIL import Image, ImageTk, ImageFilter
    except ImportError:
        print("警告: 未安装PIL库，背景图片功能将不可用")
        print("请运行: pip install Pillow")

    root = tk.Tk()

    # 设置主题样式
    style = ttk.Style()
    if "vista" in style.theme_names():
        style.theme_use("vista")
    elif "clam" in style.theme_names():
        style.theme_use("clam")

    # 配置进度条样式
    style.configure(
        'Cartoon.Horizontal.TProgressbar',
        background=CartoonStyle.COLORS['primary'],
        troughcolor=CartoonStyle.COLORS['border'],
        borderwidth=0,
        lightcolor=CartoonStyle.COLORS['primary'],
        darkcolor=CartoonStyle.COLORS['primary']
    )

    # 创建应用
    app = CSVMergerCartoonGUI(root)

    # 设置默认路径（如果存在）
    if os.path.exists("./files"):
        app.source_folder_var.set(os.path.abspath("./files"))
    if not app.output_folder_var.get():
        app.output_folder_var.set(os.path.abspath("./output"))

    # 运行应用
    root.mainloop()


if __name__ == "__main__":
    main()
