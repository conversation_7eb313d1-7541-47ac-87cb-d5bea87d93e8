#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV文件合并程序 - GUI界面
使用tkinter创建用户友好的图形界面
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from pathlib import Path
from csv_merger import CSVMerger
import logging


class CSVMergerGUI:
    """CSV合并程序GUI界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("CSV文件合并工具")
        self.root.geometry("800x700")
        self.root.resizable(True, True)
        
        # 初始化合并器
        self.merger = CSVMerger()
        
        # 界面变量
        self.source_folder_var = tk.StringVar()
        self.output_folder_var = tk.StringVar()
        self.max_depth_var = tk.IntVar(value=2)
        self.merge_mode_var = tk.StringVar(value="filename")
        self.enable_sorting_var = tk.BooleanVar(value=False)
        self.enable_dedup_var = tk.BooleanVar(value=False)
        self.sort_columns_var = tk.StringVar(value="日期")
        self.output_encoding_var = tk.StringVar(value="utf-8-sig")
        self.add_filename_column_var = tk.BooleanVar(value=False)
        self.add_folder_column_var = tk.BooleanVar(value=False)
        
        self.setup_ui()
        self.setup_logging()
        
    def setup_ui(self):
        """设置用户界面"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        row = 0
        
        # 标题
        title_label = ttk.Label(main_frame, text="CSV文件合并工具", font=("Arial", 16, "bold"))
        title_label.grid(row=row, column=0, columnspan=3, pady=(0, 20))
        row += 1
        
        # 源文件夹选择
        ttk.Label(main_frame, text="源文件夹:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.source_folder_var, width=50).grid(row=row, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=5)
        ttk.Button(main_frame, text="浏览", command=self.browse_source_folder).grid(row=row, column=2, pady=5)
        row += 1
        
        # 输出文件夹选择
        ttk.Label(main_frame, text="输出文件夹:").grid(row=row, column=0, sticky=tk.W, pady=5)
        ttk.Entry(main_frame, textvariable=self.output_folder_var, width=50).grid(row=row, column=1, sticky=(tk.W, tk.E), padx=(5, 5), pady=5)
        ttk.Button(main_frame, text="浏览", command=self.browse_output_folder).grid(row=row, column=2, pady=5)
        row += 1
        
        # 分隔线
        ttk.Separator(main_frame, orient='horizontal').grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=15)
        row += 1
        
        # 配置选项框架
        config_frame = ttk.LabelFrame(main_frame, text="配置选项", padding="10")
        config_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=5)
        config_frame.columnconfigure(1, weight=1)
        row += 1
        
        config_row = 0
        
        # 扫描深度设置
        ttk.Label(config_frame, text="扫描深度:").grid(row=config_row, column=0, sticky=tk.W, pady=5)
        depth_frame = ttk.Frame(config_frame)
        depth_frame.grid(row=config_row, column=1, sticky=(tk.W, tk.E), pady=5)

        # 创建间断拖动的Scale（使用tk.Scale而不是ttk.Scale来支持resolution）
        self.depth_scale = tk.Scale(depth_frame, from_=1, to=6, orient=tk.HORIZONTAL,
                                   variable=self.max_depth_var, command=self.on_depth_change,
                                   resolution=1, length=200, showvalue=0,
                                   troughcolor='lightgray', activebackground='lightblue')
        self.depth_scale.pack(side=tk.LEFT, fill=tk.X, expand=True)

        self.depth_label = ttk.Label(depth_frame, text="2层")
        self.depth_label.pack(side=tk.RIGHT, padx=(10, 0))
        config_row += 1
        
        # 合并模式选择
        ttk.Label(config_frame, text="合并模式:").grid(row=config_row, column=0, sticky=tk.W, pady=5)
        mode_frame = ttk.Frame(config_frame)
        mode_frame.grid(row=config_row, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Radiobutton(mode_frame, text="按文件名合并", variable=self.merge_mode_var,
                       value="filename", command=self.on_merge_mode_change).pack(side=tk.LEFT, padx=(0, 20))
        ttk.Radiobutton(mode_frame, text="直接合并", variable=self.merge_mode_var,
                       value="direct", command=self.on_merge_mode_change).pack(side=tk.LEFT)
        config_row += 1
        
        # 排序选项
        ttk.Checkbutton(config_frame, text="启用排序功能", variable=self.enable_sorting_var,
                       command=self.on_sorting_toggle).grid(row=config_row, column=0, sticky=tk.W, pady=5)
        
        sort_frame = ttk.Frame(config_frame)
        sort_frame.grid(row=config_row, column=1, sticky=(tk.W, tk.E), pady=5)
        ttk.Label(sort_frame, text="排序列:").pack(side=tk.LEFT)
        self.sort_entry = ttk.Entry(sort_frame, textvariable=self.sort_columns_var, width=30)
        self.sort_entry.pack(side=tk.LEFT, padx=(5, 0), fill=tk.X, expand=True)
        self.sort_entry.config(state='disabled')
        config_row += 1
        
        # 去重选项
        self.dedup_checkbox = ttk.Checkbutton(config_frame, text="启用去重功能（需先启用排序）",
                                            variable=self.enable_dedup_var)
        self.dedup_checkbox.grid(row=config_row, column=0, columnspan=2, sticky=tk.W, pady=5)
        self.dedup_checkbox.config(state='disabled')
        config_row += 1

        # 输出编码格式选择
        ttk.Label(config_frame, text="输出编码:").grid(row=config_row, column=0, sticky=tk.W, pady=5)
        encoding_frame = ttk.Frame(config_frame)
        encoding_frame.grid(row=config_row, column=1, sticky=(tk.W, tk.E), pady=5)

        encoding_combo = ttk.Combobox(encoding_frame, textvariable=self.output_encoding_var,
                                    values=["utf-8-sig", "utf-8", "gbk", "gb2312"],
                                    state="readonly", width=15)
        encoding_combo.pack(side=tk.LEFT)

        ttk.Label(encoding_frame, text="(UTF-8(BOM)支持Excel)").pack(side=tk.LEFT, padx=(10, 0))
        config_row += 1

        # 备注列选项
        ttk.Label(config_frame, text="备注列:").grid(row=config_row, column=0, sticky=tk.W, pady=5)
        annotation_frame = ttk.Frame(config_frame)
        annotation_frame.grid(row=config_row, column=1, sticky=(tk.W, tk.E), pady=5)

        self.filename_checkbox = ttk.Checkbutton(annotation_frame, text="备注文件名",
                                               variable=self.add_filename_column_var)
        self.filename_checkbox.pack(side=tk.LEFT, padx=(0, 20))

        self.folder_checkbox = ttk.Checkbutton(annotation_frame, text="备注文件夹名",
                                             variable=self.add_folder_column_var)
        self.folder_checkbox.pack(side=tk.LEFT)
        config_row += 1
        
        # 操作按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=row, column=0, columnspan=3, pady=20)
        row += 1
        
        self.merge_button = ttk.Button(button_frame, text="开始合并", command=self.start_merge, 
                                      style="Accent.TButton")
        self.merge_button.pack(side=tk.LEFT, padx=(0, 10))
        
        ttk.Button(button_frame, text="清空日志", command=self.clear_log).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="打开输出文件夹", command=self.open_output_folder).pack(side=tk.LEFT)
        
        # 进度条
        self.progress = ttk.Progressbar(main_frame, mode='indeterminate')
        self.progress.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        row += 1
        
        # 日志显示区域
        log_frame = ttk.LabelFrame(main_frame, text="处理日志", padding="5")
        log_frame.grid(row=row, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(row, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, width=80)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
    def setup_logging(self):
        """设置日志处理"""
        # 创建自定义日志处理器，将日志输出到GUI
        class GUILogHandler(logging.Handler):
            def __init__(self, text_widget):
                super().__init__()
                self.text_widget = text_widget
                
            def emit(self, record):
                msg = self.format(record)
                self.text_widget.insert(tk.END, msg + '\n')
                self.text_widget.see(tk.END)
                self.text_widget.update()
        
        # 配置日志
        logger = logging.getLogger('csv_merger')
        logger.setLevel(logging.INFO)
        
        # 清除现有处理器
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
            
        # 添加GUI处理器
        gui_handler = GUILogHandler(self.log_text)
        gui_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        logger.addHandler(gui_handler)
        
    def on_depth_change(self, value):
        """深度滑块变化事件"""
        depth = int(float(value))
        self.depth_label.config(text=f"{depth}层")
        
    def on_sorting_toggle(self):
        """排序选项切换事件"""
        if self.enable_sorting_var.get():
            self.sort_entry.config(state='normal')
            self.dedup_checkbox.config(state='normal')
        else:
            self.sort_entry.config(state='disabled')
            self.dedup_checkbox.config(state='disabled')
            self.enable_dedup_var.set(False)

    def on_merge_mode_change(self):
        """合并模式切换事件"""
        if self.merge_mode_var.get() == "direct":
            # 直接合并模式强制勾选文件名备注列
            self.add_filename_column_var.set(True)
            self.filename_checkbox.config(state='disabled')
        else:
            # 按文件名合并模式可以自由选择
            self.filename_checkbox.config(state='normal')
            
    def browse_source_folder(self):
        """浏览源文件夹"""
        folder = filedialog.askdirectory(title="选择源文件夹")
        if folder:
            self.source_folder_var.set(folder)
            
    def browse_output_folder(self):
        """浏览输出文件夹"""
        folder = filedialog.askdirectory(title="选择输出文件夹")
        if folder:
            self.output_folder_var.set(folder)
            
    def clear_log(self):
        """清空日志"""
        self.log_text.delete(1.0, tk.END)
        
    def open_output_folder(self):
        """打开输出文件夹"""
        output_folder = self.output_folder_var.get()
        if output_folder and os.path.exists(output_folder):
            os.startfile(output_folder)
        else:
            messagebox.showwarning("警告", "输出文件夹不存在或未设置")
            
    def validate_inputs(self):
        """验证输入参数"""
        if not self.source_folder_var.get():
            messagebox.showerror("错误", "请选择源文件夹")
            return False
            
        if not self.output_folder_var.get():
            messagebox.showerror("错误", "请选择输出文件夹")
            return False
            
        if not os.path.exists(self.source_folder_var.get()):
            messagebox.showerror("错误", "源文件夹不存在")
            return False
            
        return True

    def start_merge(self):
        """开始合并处理"""
        if not self.validate_inputs():
            return

        # 禁用合并按钮，显示进度条
        self.merge_button.config(state='disabled')
        self.progress.start()

        # 在后台线程中执行合并
        thread = threading.Thread(target=self.perform_merge)
        thread.daemon = True
        thread.start()

    def perform_merge(self):
        """执行合并操作（在后台线程中运行）"""
        try:
            # 配置合并器参数
            sort_columns = []
            if self.enable_sorting_var.get() and self.sort_columns_var.get().strip():
                sort_columns = [col.strip() for col in self.sort_columns_var.get().split(',')]

            self.merger.set_config(
                max_depth=self.max_depth_var.get(),
                merge_by_filename=(self.merge_mode_var.get() == "filename"),
                enable_sorting=self.enable_sorting_var.get(),
                sort_columns=sort_columns,
                enable_deduplication=self.enable_dedup_var.get(),
                output_encoding=self.output_encoding_var.get(),
                add_filename_column=self.add_filename_column_var.get(),
                add_folder_column=self.add_folder_column_var.get()
            )

            # 执行合并
            result = self.merger.merge_files(
                self.source_folder_var.get(),
                self.output_folder_var.get()
            )

            # 在主线程中显示结果
            self.root.after(0, self.merge_completed, result)

        except Exception as e:
            error_msg = f"合并过程中发生错误: {str(e)}"
            self.root.after(0, self.merge_error, error_msg)

    def merge_completed(self, result):
        """合并完成回调"""
        self.progress.stop()
        self.merge_button.config(state='normal')

        if result.get('success', False):
            message = f"""合并完成！

处理统计：
• 总文件数: {result['total_files']}
• 合并组数: {result['merged_groups']}
• 孤立文件数: {result['orphan_files']}
• 输出文件夹: {result['output_folder']}"""

            messagebox.showinfo("合并完成", message)
        else:
            error_msg = result.get('error', '未知错误')
            messagebox.showerror("合并失败", f"合并失败: {error_msg}")

    def merge_error(self, error_msg):
        """合并错误回调"""
        self.progress.stop()
        self.merge_button.config(state='normal')
        messagebox.showerror("错误", error_msg)


def main():
    """主函数"""
    root = tk.Tk()

    # 设置主题样式
    style = ttk.Style()
    if "vista" in style.theme_names():
        style.theme_use("vista")
    elif "clam" in style.theme_names():
        style.theme_use("clam")

    # 创建应用
    app = CSVMergerGUI(root)

    # 设置默认路径（如果存在）
    if os.path.exists("./files"):
        app.source_folder_var.set(os.path.abspath("./files"))
    if not app.output_folder_var.get():
        app.output_folder_var.set(os.path.abspath("./output"))

    # 运行应用
    root.mainloop()


if __name__ == "__main__":
    main()
