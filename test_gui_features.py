#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI新功能测试脚本
测试编码格式选择、备注列功能、间断拖动条等新功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(__file__))

from csv_merger_gui import CSVMergerGUI


def test_gui_features():
    """测试GUI新功能"""
    print("=== GUI新功能测试 ===")
    
    # 创建测试窗口
    root = tk.Tk()
    root.title("CSV合并工具 - 功能测试")
    
    try:
        # 创建GUI应用
        app = CSVMergerGUI(root)
        
        print("✅ GUI界面创建成功")
        
        # 测试默认值
        print(f"✅ 默认扫描深度: {app.max_depth_var.get()}")
        print(f"✅ 默认合并模式: {app.merge_mode_var.get()}")
        print(f"✅ 默认编码格式: {app.output_encoding_var.get()}")
        print(f"✅ 默认文件名备注: {app.add_filename_column_var.get()}")
        print(f"✅ 默认文件夹备注: {app.add_folder_column_var.get()}")
        
        # 测试拖动条间断功能
        print("\n--- 测试拖动条间断功能 ---")
        for i in range(1, 7):
            app.max_depth_var.set(i)
            print(f"✅ 设置深度为 {i}: {app.max_depth_var.get()}")
        
        # 测试合并模式切换
        print("\n--- 测试合并模式切换 ---")
        app.merge_mode_var.set("filename")
        app.on_merge_mode_change()
        print(f"✅ 按文件名合并模式 - 文件名备注状态: {app.filename_checkbox['state']}")
        
        app.merge_mode_var.set("direct")
        app.on_merge_mode_change()
        print(f"✅ 直接合并模式 - 文件名备注状态: {app.filename_checkbox['state']}")
        print(f"✅ 直接合并模式 - 文件名备注值: {app.add_filename_column_var.get()}")
        
        # 测试编码格式选择
        print("\n--- 测试编码格式选择 ---")
        encoding_options = ["utf-8-sig", "utf-8", "gbk", "gb2312"]
        for encoding in encoding_options:
            app.output_encoding_var.set(encoding)
            print(f"✅ 设置编码为: {encoding}")
        
        # 测试排序功能切换
        print("\n--- 测试排序功能切换 ---")
        app.enable_sorting_var.set(False)
        app.on_sorting_toggle()
        print(f"✅ 排序关闭 - 排序输入框状态: {app.sort_entry['state']}")
        print(f"✅ 排序关闭 - 去重复选框状态: {app.dedup_checkbox['state']}")
        
        app.enable_sorting_var.set(True)
        app.on_sorting_toggle()
        print(f"✅ 排序开启 - 排序输入框状态: {app.sort_entry['state']}")
        print(f"✅ 排序开启 - 去重复选框状态: {app.dedup_checkbox['state']}")
        
        print("\n=== 所有GUI功能测试通过 ===")
        
        # 设置测试数据路径
        if os.path.exists("./files"):
            app.source_folder_var.set(os.path.abspath("./files"))
            print(f"✅ 设置源文件夹: {app.source_folder_var.get()}")
        
        if not app.output_folder_var.get():
            app.output_folder_var.set(os.path.abspath("./test_gui_output"))
            print(f"✅ 设置输出文件夹: {app.output_folder_var.get()}")
        
        print("\n界面已准备就绪，您可以手动测试各项功能")
        print("按Ctrl+C或关闭窗口退出测试")
        
        # 运行GUI
        root.mainloop()
        
    except Exception as e:
        print(f"❌ GUI测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            root.destroy()
        except:
            pass


def test_scale_functionality():
    """单独测试Scale控件功能"""
    print("\n=== Scale控件功能测试 ===")
    
    root = tk.Tk()
    root.title("Scale控件测试")
    root.geometry("400x200")
    
    # 创建测试变量
    scale_var = tk.IntVar(value=2)
    
    # 创建标签
    label = tk.Label(root, text="测试间断拖动Scale控件", font=("Arial", 12))
    label.pack(pady=10)
    
    # 创建Scale控件
    scale = tk.Scale(root, from_=1, to=6, orient=tk.HORIZONTAL, 
                    variable=scale_var, resolution=1, length=300,
                    showvalue=1, troughcolor='lightgray', 
                    activebackground='lightblue')
    scale.pack(pady=10)
    
    # 创建显示标签
    value_label = tk.Label(root, text=f"当前值: {scale_var.get()}", font=("Arial", 10))
    value_label.pack(pady=5)
    
    def on_scale_change(value):
        value_label.config(text=f"当前值: {value}")
        print(f"Scale值变化: {value}")
    
    scale.config(command=on_scale_change)
    
    # 创建关闭按钮
    close_btn = tk.Button(root, text="关闭测试", command=root.destroy)
    close_btn.pack(pady=10)
    
    print("✅ Scale控件创建成功")
    print("请拖动滑块测试间断功能（只能选择1-6的整数值）")
    
    root.mainloop()


def main():
    """主函数"""
    print("CSV合并工具 - GUI新功能测试")
    print("=" * 50)
    
    choice = input("选择测试模式:\n1. 完整GUI功能测试\n2. Scale控件单独测试\n请输入选择 (1/2): ").strip()
    
    if choice == "1":
        test_gui_features()
    elif choice == "2":
        test_scale_functionality()
    else:
        print("无效选择，启动完整GUI功能测试")
        test_gui_features()


if __name__ == "__main__":
    main()
