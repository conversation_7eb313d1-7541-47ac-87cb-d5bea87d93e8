#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV合并工具测试脚本
"""

import os
import shutil
from pathlib import Path
from csv_merger import CSVMerger


def test_basic_functionality():
    """测试基本功能"""
    print("=== 测试基本功能 ===")
    
    merger = CSVMerger()
    
    # 测试1: 按文件名合并
    print("\n1. 测试按文件名合并模式")
    merger.set_config(
        max_depth=3,
        merge_by_filename=True,
        enable_sorting=True,
        sort_columns=["日期"],
        enable_deduplication=True
    )
    
    result = merger.merge_files("./files", "./test_output_filename")
    print(f"结果: {result}")
    
    # 测试2: 直接合并
    print("\n2. 测试直接合并模式")
    merger.set_config(
        max_depth=3,
        merge_by_filename=False,
        enable_sorting=True,
        sort_columns=["日期", "当前井深"],
        enable_deduplication=False
    )
    
    result = merger.merge_files("./files", "./test_output_direct")
    print(f"结果: {result}")
    
    # 测试3: 不同扫描深度
    print("\n3. 测试不同扫描深度")
    merger.set_config(max_depth=1, merge_by_filename=True)
    result = merger.merge_files("./files", "./test_output_depth1")
    print(f"深度1结果: {result}")
    
    merger.set_config(max_depth=2, merge_by_filename=True)
    result = merger.merge_files("./files", "./test_output_depth2")
    print(f"深度2结果: {result}")


def test_file_scanning():
    """测试文件扫描功能"""
    print("\n=== 测试文件扫描功能 ===")
    
    merger = CSVMerger()
    
    # 测试不同深度的扫描
    for depth in [1, 2, 3]:
        merger.set_config(max_depth=depth)
        files = merger.scan_csv_files("./files")
        print(f"深度 {depth}: 找到 {len(files)} 个文件")
        
        # 显示前3个文件的信息
        for i, file_info in enumerate(files[:3]):
            print(f"  文件{i+1}: {file_info['filename']} (深度: {file_info['depth']}, 文件夹: {file_info['parent_folder']})")


def test_header_classification():
    """测试表头分类功能"""
    print("\n=== 测试表头分类功能 ===")
    
    merger = CSVMerger()
    merger.set_config(max_depth=3)
    
    files = merger.scan_csv_files("./files")
    header_groups = merger.classify_files_by_headers(files)
    
    print(f"发现 {len(header_groups)} 个不同的表头组:")
    for i, (signature, file_list) in enumerate(header_groups.items()):
        print(f"  组{i+1}: {len(file_list)} 个文件")
        print(f"    表头: {file_list[0]['headers'][:3]}...")  # 显示前3个表头
        print(f"    文件: {[f['filename'] for f in file_list[:3]]}")  # 显示前3个文件名


def test_sorting_and_deduplication():
    """测试排序和去重功能"""
    print("\n=== 测试排序和去重功能 ===")
    
    merger = CSVMerger()
    
    # 测试排序功能
    print("\n1. 测试排序功能")
    merger.set_config(
        max_depth=3,
        merge_by_filename=True,
        enable_sorting=True,
        sort_columns=["日期"],
        enable_deduplication=False
    )
    
    result = merger.merge_files("./files", "./test_output_sort_only")
    print(f"仅排序结果: {result}")
    
    # 测试排序+去重功能
    print("\n2. 测试排序+去重功能")
    merger.set_config(
        max_depth=3,
        merge_by_filename=True,
        enable_sorting=True,
        sort_columns=["日期"],
        enable_deduplication=True
    )
    
    result = merger.merge_files("./files", "./test_output_sort_dedup")
    print(f"排序+去重结果: {result}")


def cleanup_test_outputs():
    """清理测试输出文件夹"""
    print("\n=== 清理测试输出 ===")
    
    test_folders = [
        "./test_output_filename",
        "./test_output_direct", 
        "./test_output_depth1",
        "./test_output_depth2",
        "./test_output_sort_only",
        "./test_output_sort_dedup"
    ]
    
    for folder in test_folders:
        if os.path.exists(folder):
            shutil.rmtree(folder)
            print(f"已删除: {folder}")


def main():
    """主测试函数"""
    print("CSV合并工具功能测试")
    print("=" * 50)
    
    # 检查测试数据是否存在
    if not os.path.exists("./files"):
        print("错误: 找不到测试数据文件夹 './files'")
        return
    
    try:
        # 运行各项测试
        test_file_scanning()
        test_header_classification()
        test_basic_functionality()
        test_sorting_and_deduplication()
        
        print("\n" + "=" * 50)
        print("所有测试完成！")
        print("\n请检查以下输出文件夹:")
        print("- test_output_filename (按文件名合并)")
        print("- test_output_direct (直接合并)")
        print("- test_output_depth1 (深度1扫描)")
        print("- test_output_depth2 (深度2扫描)")
        print("- test_output_sort_only (仅排序)")
        print("- test_output_sort_dedup (排序+去重)")
        
        # 询问是否清理测试输出
        response = input("\n是否清理测试输出文件夹? (y/n): ").lower().strip()
        if response == 'y':
            cleanup_test_outputs()
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
