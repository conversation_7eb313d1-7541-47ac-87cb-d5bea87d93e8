# CSV文件合并工具

一个功能强大的CSV文件合并工具，支持多种合并模式、排序和去重功能，并提供友好的图形界面。

## 功能特性

### 🔍 智能文件扫描
- 支持1-6层文件夹递归扫描（默认2层）
- 自动识别所有CSV文件
- 智能表头一致性检查

### 📊 灵活合并模式
1. **按文件名合并**：将同名CSV文件合并为一个文件
2. **直接合并**：将所有表头一致的CSV文件合并在一起

### 🗂️ 智能文件分类
- 表头一致的文件自动分组合并
- 表头不一致或单独文件放入`Orphan`文件夹
- 孤立文件命名格式：`原名称(原文件夹名称).csv`

### 🔄 高级数据处理
- **多列排序**：支持多个列的优先级排序
- **去重功能**：可选择去除重复行（需启用排序）
- **数据验证**：自动检查数据完整性

### 🖥️ 用户友好界面
- 直观的图形界面（GUI）
- 实时处理日志显示
- 进度条显示处理状态
- 一键打开输出文件夹

## 安装要求

```bash
pip install pandas tkinter
```

注：tkinter通常随Python标准库安装，pandas需要单独安装。

## 使用方法

### 图形界面模式（推荐）

```bash
python main.py
# 或
python main.py --gui
```

### 命令行模式

```bash
python main.py --cli
```

### 帮助信息

```bash
python main.py --help
```

## 使用步骤

### GUI界面使用

1. **启动程序**：运行 `python main.py`
2. **选择源文件夹**：点击"浏览"按钮选择包含CSV文件的文件夹
3. **选择输出文件夹**：选择合并后文件的保存位置
4. **配置选项**：
   - 调整扫描深度（1-6层）
   - 选择合并模式（按文件名/直接合并）
   - 可选启用排序功能并设置排序列
   - 可选启用去重功能
5. **开始合并**：点击"开始合并"按钮
6. **查看结果**：在日志区域查看处理过程，完成后可点击"打开输出文件夹"

### 配置说明

#### 扫描深度
- **1层**：仅扫描选定文件夹内的CSV文件
- **2层**：扫描选定文件夹及其子文件夹（默认）
- **3-6层**：递归扫描更深层次的子文件夹

#### 合并模式
- **按文件名合并**：
  - 将文件名相同的CSV文件合并
  - 输出文件名：`原文件名_merged.csv`
  - 适用于同一数据源的多个时间段文件
  
- **直接合并**：
  - 将所有表头一致的CSV文件合并为一个文件
  - 输出文件名：`merged_N_files.csv`（N为文件数量）
  - 适用于需要汇总所有数据的场景

#### 排序功能
- 支持多列排序，用逗号分隔列名
- 例如：`日期,当前井深` 表示先按日期排序，再按当前井深排序
- 默认按第一列排序

#### 去重功能
- 仅在启用排序后可用
- 基于所有列的值判断重复行
- 保留第一次出现的行

## 输出结构

```
输出文件夹/
├── 合并文件1_merged.csv
├── 合并文件2_merged.csv
├── ...
└── Orphan/
    ├── 孤立文件1(文件夹名).csv
    └── 孤立文件2(文件夹名).csv
```

## 示例

假设有以下文件结构：
```
files/
├── 01/
│   ├── 蓬深1-钻井日报.csv
│   ├── 蓬深2-钻井日报.csv
│   └── 蓬深3-钻井日报.csv
├── 02/
│   ├── 蓬深2-钻井日报.csv
│   ├── 蓬深3-钻井日报.csv
│   └── 蓬深4-钻井日报.csv
└── 03/
    ├── 东坝1-钻井日报.csv  (表头不同)
    └── 蓬深5-钻井日报.csv
```

**按文件名合并模式**输出：
```
output/
├── 蓬深2-钻井日报_merged.csv  (合并01和02文件夹中的同名文件)
├── 蓬深3-钻井日报_merged.csv  (合并01和02文件夹中的同名文件)
└── Orphan/
    ├── 蓬深1-钻井日报(01).csv  (只在01文件夹中存在)
    ├── 蓬深4-钻井日报(02).csv  (只在02文件夹中存在)
    ├── 蓬深5-钻井日报(03).csv  (只在03文件夹中存在)
    └── 东坝1-钻井日报(03).csv  (表头不同)
```

## 注意事项

1. **编码支持**：程序使用UTF-8编码读写CSV文件
2. **内存使用**：大文件合并时请确保有足够内存
3. **文件备份**：建议在处理重要数据前先备份原文件
4. **表头匹配**：表头必须完全一致才能合并（包括顺序和名称）
5. **日期格式**：排序功能对日期格式敏感，建议使用标准格式

## 故障排除

### 常见问题

1. **"无法读取文件表头"**
   - 检查CSV文件是否损坏
   - 确认文件编码为UTF-8
   - 检查文件是否被其他程序占用

2. **"表头与预期不一致"**
   - 这是警告信息，不影响合并
   - 表示文件的实际表头与分组时检测的表头略有差异

3. **合并后数据丢失**
   - 检查源文件是否包含空行
   - 确认CSV格式是否标准

4. **GUI界面无响应**
   - 大文件处理时属正常现象
   - 查看日志区域了解处理进度

## 技术支持

如遇到问题，请检查：
1. Python版本（建议3.7+）
2. 依赖包是否正确安装
3. 文件权限是否足够
4. 磁盘空间是否充足

## 更新日志

### v1.0.0
- 初始版本发布
- 支持基本的CSV文件合并功能
- 提供GUI和CLI两种使用方式
- 支持多层文件夹扫描
- 支持排序和去重功能
