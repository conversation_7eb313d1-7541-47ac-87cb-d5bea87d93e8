#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试编码格式功能
"""

import os
import pandas as pd
from csv_merger import CSVMerger


def test_encoding_formats():
    """测试不同编码格式的输出"""
    print("=== 测试编码格式功能 ===")
    
    merger = CSVMerger()
    
    # 测试所有支持的编码格式
    for encoding_name, encoding_value in merger.encoding_options.items():
        print(f"\n测试编码格式: {encoding_name} ({encoding_value})")
        
        # 配置合并器
        merger.set_config(
            max_depth=2,
            merge_by_filename=True,
            enable_sorting=True,
            sort_columns=["日期"],
            enable_deduplication=True,
            output_encoding=encoding_value
        )
        
        # 创建输出文件夹
        output_folder = f"./test_encoding_{encoding_name.replace('(', '_').replace(')', '_').replace('-', '_')}"
        
        try:
            # 执行合并
            result = merger.merge_files("./files", output_folder)
            
            if result.get('success', False):
                print(f"✅ {encoding_name} 编码测试成功")
                print(f"   输出文件夹: {output_folder}")
                print(f"   处理文件数: {result['total_files']}")
                print(f"   合并组数: {result['merged_groups']}")
                
                # 验证文件是否可以正确读取
                test_file_path = None
                for file in os.listdir(output_folder):
                    if file.endswith('.csv') and not file.startswith('Orphan'):
                        test_file_path = os.path.join(output_folder, file)
                        break
                
                if test_file_path:
                    try:
                        # 尝试读取文件验证编码
                        df = pd.read_csv(test_file_path, encoding=encoding_value)
                        print(f"   ✅ 文件读取验证成功，行数: {len(df)}")
                        
                        # 检查BOM
                        with open(test_file_path, 'rb') as f:
                            first_bytes = f.read(3)
                            if first_bytes == b'\xef\xbb\xbf':
                                print(f"   📝 检测到UTF-8 BOM标记")
                            else:
                                print(f"   📝 无BOM标记")
                                
                    except Exception as e:
                        print(f"   ❌ 文件读取验证失败: {e}")
                        
            else:
                error_msg = result.get('error', '未知错误')
                print(f"❌ {encoding_name} 编码测试失败: {error_msg}")
                
        except Exception as e:
            print(f"❌ {encoding_name} 编码测试异常: {e}")


def test_excel_compatibility():
    """测试Excel兼容性"""
    print("\n=== 测试Excel兼容性 ===")
    
    merger = CSVMerger()
    
    # 使用UTF-8(BOM)编码，这是Excel推荐的格式
    merger.set_config(
        max_depth=2,
        merge_by_filename=True,
        enable_sorting=True,
        sort_columns=["日期"],
        enable_deduplication=True,
        output_encoding="utf-8-sig"  # UTF-8(BOM)
    )
    
    output_folder = "./test_excel_compatibility"
    
    try:
        result = merger.merge_files("./files", output_folder)
        
        if result.get('success', False):
            print("✅ Excel兼容性测试成功")
            
            # 检查生成的文件
            for file in os.listdir(output_folder):
                if file.endswith('.csv'):
                    file_path = os.path.join(output_folder, file)
                    
                    # 检查BOM标记
                    with open(file_path, 'rb') as f:
                        first_bytes = f.read(3)
                        if first_bytes == b'\xef\xbb\xbf':
                            print(f"   ✅ {file} 包含UTF-8 BOM，Excel兼容")
                        else:
                            print(f"   ⚠️ {file} 不包含BOM标记")
                    
                    # 验证中文字符
                    try:
                        df = pd.read_csv(file_path, encoding='utf-8-sig')
                        chinese_columns = [col for col in df.columns if any('\u4e00' <= char <= '\u9fff' for char in col)]
                        if chinese_columns:
                            print(f"   ✅ {file} 中文列名正常: {chinese_columns[:3]}...")
                    except Exception as e:
                        print(f"   ❌ {file} 读取失败: {e}")
        else:
            print("❌ Excel兼容性测试失败")
            
    except Exception as e:
        print(f"❌ Excel兼容性测试异常: {e}")


def cleanup_test_folders():
    """清理测试文件夹"""
    print("\n=== 清理测试文件夹 ===")
    
    import shutil
    
    test_folders = [
        "./test_encoding_UTF_8_BOM_",
        "./test_encoding_UTF_8",
        "./test_encoding_GBK",
        "./test_encoding_GB2312",
        "./test_encoding_ASCII",
        "./test_excel_compatibility"
    ]
    
    for folder in test_folders:
        if os.path.exists(folder):
            try:
                shutil.rmtree(folder)
                print(f"✅ 已删除: {folder}")
            except Exception as e:
                print(f"❌ 删除失败 {folder}: {e}")


def main():
    """主测试函数"""
    print("CSV合并工具编码格式测试")
    print("=" * 50)
    
    # 检查测试数据是否存在
    if not os.path.exists("./files"):
        print("错误: 找不到测试数据文件夹 './files'")
        return
    
    try:
        # 运行编码格式测试
        test_encoding_formats()
        
        # 运行Excel兼容性测试
        test_excel_compatibility()
        
        print("\n" + "=" * 50)
        print("编码格式测试完成！")
        
        # 询问是否清理测试输出
        response = input("\n是否清理测试输出文件夹? (y/n): ").lower().strip()
        if response == 'y':
            cleanup_test_folders()
        
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
