#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSV文件合并程序
功能：
1. 支持1-6层文件夹扫描（默认2层）
2. 按文件名合并或直接合并
3. 表头一致性检查
4. 孤立文件处理
5. 多列排序功能
6. 去重功能
7. GUI界面
"""

import os
import csv
import pandas as pd
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Set
import logging
from collections import defaultdict
import shutil

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class CSVMerger:
    """CSV文件合并器核心类"""
    
    def __init__(self):
        self.max_depth = 2  # 默认扫描2层
        self.merge_by_filename = True  # 默认按文件名合并
        self.enable_sorting = False  # 默认不启用排序
        self.sort_columns = []  # 排序列名列表，按优先级排序
        self.enable_deduplication = False  # 默认不启用去重
        self.source_folder = ""  # 源文件夹路径
        self.output_folder = ""  # 输出文件夹路径
        self.output_encoding = "utf-8-sig"  # 默认使用UTF-8(BOM)编码，兼容Excel

        # 支持的编码格式
        self.encoding_options = {
            "UTF-8(BOM)": "utf-8-sig",
            "UTF-8": "utf-8",
            "GBK": "gbk",
            "GB2312": "gb2312",
            "ASCII": "ascii"
        }
        
    def set_config(self, max_depth: int = 2, merge_by_filename: bool = True,
                   enable_sorting: bool = False, sort_columns: List[str] = None,
                   enable_deduplication: bool = False, output_encoding: str = "utf-8-sig"):
        """设置配置参数"""
        self.max_depth = max_depth
        self.merge_by_filename = merge_by_filename
        self.enable_sorting = enable_sorting
        self.sort_columns = sort_columns or []
        self.enable_deduplication = enable_deduplication
        self.output_encoding = output_encoding
        
    def scan_csv_files(self, folder_path: str) -> List[Dict]:
        """
        扫描指定文件夹中的CSV文件
        返回文件信息列表，包含文件路径、文件名、所在文件夹等信息
        """
        csv_files = []
        folder_path = Path(folder_path)
        
        def _scan_recursive(current_path: Path, current_depth: int):
            if current_depth > self.max_depth:
                return
                
            try:
                for item in current_path.iterdir():
                    if item.is_file() and item.suffix.lower() == '.csv':
                        # 获取相对于源文件夹的路径信息
                        relative_path = item.relative_to(folder_path)
                        parent_folder = relative_path.parent.name if relative_path.parent != Path('.') else ''
                        
                        csv_files.append({
                            'full_path': str(item),
                            'filename': item.stem,  # 不含扩展名的文件名
                            'filename_with_ext': item.name,  # 含扩展名的文件名
                            'parent_folder': parent_folder,
                            'relative_path': str(relative_path),
                            'depth': current_depth
                        })
                    elif item.is_dir() and current_depth < self.max_depth:
                        _scan_recursive(item, current_depth + 1)
            except PermissionError:
                logger.warning(f"无法访问文件夹: {current_path}")
                
        _scan_recursive(folder_path, 1)
        logger.info(f"扫描完成，找到 {len(csv_files)} 个CSV文件")
        return csv_files
    
    def get_csv_headers(self, file_path: str) -> List[str]:
        """获取CSV文件的表头"""
        # 尝试多种编码格式读取文件
        encodings_to_try = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']

        for encoding in encodings_to_try:
            try:
                with open(file_path, 'r', encoding=encoding, newline='') as f:
                    reader = csv.reader(f)
                    headers = next(reader)
                    return [header.strip() for header in headers]
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                logger.error(f"读取文件表头失败 {file_path}: {e}")
                break

        logger.error(f"无法以任何编码格式读取文件表头: {file_path}")
        return []
    
    def classify_files_by_headers(self, csv_files: List[Dict]) -> Dict[str, List[Dict]]:
        """
        根据表头对文件进行分类
        返回字典：表头签名 -> 文件列表
        """
        header_groups = defaultdict(list)
        
        for file_info in csv_files:
            headers = self.get_csv_headers(file_info['full_path'])
            if headers:
                # 使用表头的字符串表示作为分组键
                header_signature = '|'.join(headers)
                file_info['headers'] = headers
                file_info['header_signature'] = header_signature
                header_groups[header_signature].append(file_info)
            else:
                logger.warning(f"无法读取文件表头: {file_info['full_path']}")
                
        logger.info(f"文件分类完成，共 {len(header_groups)} 个不同的表头组")
        return dict(header_groups)
    
    def group_files_for_merging(self, header_groups: Dict[str, List[Dict]]) -> Tuple[Dict, List[Dict]]:
        """
        根据合并模式对文件进行分组
        返回：(可合并的文件组, 孤立文件列表)
        """
        mergeable_groups = {}
        orphan_files = []
        
        for header_signature, files in header_groups.items():
            if len(files) == 1:
                # 只有一个文件的组，标记为孤立文件
                orphan_files.extend(files)
                continue
                
            if self.merge_by_filename:
                # 按文件名分组
                filename_groups = defaultdict(list)
                for file_info in files:
                    filename_groups[file_info['filename']].append(file_info)
                
                for filename, file_list in filename_groups.items():
                    if len(file_list) > 1:
                        # 有多个同名文件，可以合并
                        group_key = f"{header_signature}_{filename}"
                        mergeable_groups[group_key] = {
                            'files': file_list,
                            'output_filename': f"{filename}_merged.csv",
                            'headers': file_list[0]['headers']
                        }
                    else:
                        # 只有一个同名文件，标记为孤立文件
                        orphan_files.extend(file_list)
            else:
                # 直接合并所有表头相同的文件
                group_key = f"direct_merge_{header_signature}"
                mergeable_groups[group_key] = {
                    'files': files,
                    'output_filename': f"merged_{len(files)}_files.csv",
                    'headers': files[0]['headers']
                }
        
        logger.info(f"分组完成：{len(mergeable_groups)} 个可合并组，{len(orphan_files)} 个孤立文件")
        return mergeable_groups, orphan_files
    
    def read_csv_data(self, file_path: str, headers: List[str]) -> pd.DataFrame:
        """读取CSV文件数据"""
        # 尝试多种编码格式读取文件
        encodings_to_try = ['utf-8-sig', 'utf-8', 'gbk', 'gb2312']

        for encoding in encodings_to_try:
            try:
                df = pd.read_csv(file_path, encoding=encoding)
                # 确保列名一致
                if list(df.columns) != headers:
                    logger.warning(f"文件 {file_path} 的表头与预期不一致")
                return df
            except (UnicodeDecodeError, UnicodeError):
                continue
            except Exception as e:
                logger.error(f"读取CSV文件失败 {file_path}: {e}")
                break

        logger.error(f"无法以任何编码格式读取文件: {file_path}")
        return pd.DataFrame()
    
    def merge_csv_group(self, file_group: Dict) -> pd.DataFrame:
        """合并一组CSV文件"""
        all_data = []
        headers = file_group['headers']
        
        for file_info in file_group['files']:
            df = self.read_csv_data(file_info['full_path'], headers)
            if not df.empty:
                all_data.append(df)
                logger.info(f"已读取文件: {file_info['filename_with_ext']} ({len(df)} 行)")
        
        if not all_data:
            logger.warning("没有有效的数据可以合并")
            return pd.DataFrame()
        
        # 合并所有数据
        merged_df = pd.concat(all_data, ignore_index=True)
        logger.info(f"合并完成，总计 {len(merged_df)} 行数据")
        
        # 应用排序
        if self.enable_sorting and self.sort_columns:
            valid_sort_columns = [col for col in self.sort_columns if col in merged_df.columns]
            if valid_sort_columns:
                merged_df = merged_df.sort_values(by=valid_sort_columns)
                logger.info(f"已按 {valid_sort_columns} 排序")
        
        # 应用去重
        if self.enable_deduplication and self.enable_sorting:
            original_count = len(merged_df)
            merged_df = merged_df.drop_duplicates()
            removed_count = original_count - len(merged_df)
            if removed_count > 0:
                logger.info(f"已去除 {removed_count} 行重复数据")
        
        return merged_df

    def process_orphan_files(self, orphan_files: List[Dict], output_folder: str):
        """处理孤立文件，复制到Orphan文件夹"""
        if not orphan_files:
            return

        orphan_folder = Path(output_folder) / "Orphan"
        orphan_folder.mkdir(parents=True, exist_ok=True)

        for file_info in orphan_files:
            # 构造新文件名：原名称+（原文件夹名称）
            if file_info['parent_folder']:
                new_filename = f"{file_info['filename']}({file_info['parent_folder']}).csv"
            else:
                new_filename = f"{file_info['filename']}(root).csv"

            source_path = Path(file_info['full_path'])
            target_path = orphan_folder / new_filename

            try:
                shutil.copy2(source_path, target_path)
                logger.info(f"孤立文件已复制: {new_filename}")
            except Exception as e:
                logger.error(f"复制孤立文件失败 {source_path}: {e}")

    def save_merged_data(self, merged_df: pd.DataFrame, output_path: str):
        """保存合并后的数据"""
        try:
            merged_df.to_csv(output_path, index=False, encoding=self.output_encoding)
            encoding_name = [k for k, v in self.encoding_options.items() if v == self.output_encoding][0]
            logger.info(f"合并文件已保存: {output_path} (编码: {encoding_name})")
        except Exception as e:
            logger.error(f"保存合并文件失败 {output_path}: {e}")

    def merge_files(self, source_folder: str, output_folder: str) -> Dict:
        """
        执行完整的文件合并流程
        返回处理结果统计
        """
        self.source_folder = source_folder
        self.output_folder = output_folder

        # 创建输出文件夹
        Path(output_folder).mkdir(parents=True, exist_ok=True)

        # 1. 扫描CSV文件
        csv_files = self.scan_csv_files(source_folder)
        if not csv_files:
            return {"error": "未找到CSV文件"}

        # 2. 按表头分类
        header_groups = self.classify_files_by_headers(csv_files)

        # 3. 分组准备合并
        mergeable_groups, orphan_files = self.group_files_for_merging(header_groups)

        # 4. 处理孤立文件
        self.process_orphan_files(orphan_files, output_folder)

        # 5. 合并文件组
        merged_count = 0
        for group_key, file_group in mergeable_groups.items():
            merged_df = self.merge_csv_group(file_group)
            if not merged_df.empty:
                output_path = Path(output_folder) / file_group['output_filename']
                self.save_merged_data(merged_df, str(output_path))
                merged_count += 1

        # 返回处理结果
        result = {
            "total_files": len(csv_files),
            "merged_groups": len(mergeable_groups),
            "orphan_files": len(orphan_files),
            "output_folder": output_folder,
            "success": True
        }

        logger.info(f"合并完成: {result}")
        return result


def main():
    """命令行测试入口"""
    merger = CSVMerger()

    # 测试配置
    source_folder = "./files"
    output_folder = "./output"

    # 配置参数
    merger.set_config(
        max_depth=3,
        merge_by_filename=True,
        enable_sorting=True,
        sort_columns=["日期"],
        enable_deduplication=True
    )

    # 执行合并
    result = merger.merge_files(source_folder, output_folder)
    print(f"处理结果: {result}")


if __name__ == "__main__":
    main()
